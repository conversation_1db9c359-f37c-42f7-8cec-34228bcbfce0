<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Guides - Live Scan & Notary Services | Anshin Mobile Notary</title>
    <meta name="description" content="Essential video guides for Live Scan fingerprinting and notary services. Learn what to bring, how the process works, and tips for a smooth experience.">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: #d2ab68;
            --primary-dark: #b8956b;
            --secondary-color: #2c3e50;
            --text-primary: #2c3e50;
            --text-secondary: #5a6c7d;
            --text-light: #8492a6;
            --white: #ffffff;
            --light-bg: #f8f9fa;
            --border-color: #e9ecef;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 4px 20px rgba(0,0,0,0.15);
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-body: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-display: 'Playfair Display', Georgia, serif;
        }

        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-body);
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--white);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Page Title Section */
        .page-title {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
            color: var(--white);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pattern" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
            pointer-events: none;
        }

        .page-title-content {
            position: relative;
            z-index: 2;
        }

        .page-title h1 {
            font-family: var(--font-display);
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 600;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .subtitle {
            font-size: clamp(1.1rem, 2.5vw, 1.3rem);
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.5;
        }

        /* Stats Section */
        .stats-section {
            padding: 60px 0;
            background: var(--white);
            border-bottom: 1px solid var(--border-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }

        .stat-item {
            text-align: center;
            padding: 30px 20px;
            background: linear-gradient(135deg, var(--light-bg) 0%, var(--white) 100%);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            font-family: var(--font-display);
            margin-bottom: 10px;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 0.95rem;
        }

        /* Section Header */
        .section-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .section-header h2 {
            font-family: var(--font-display);
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .section-header p {
            font-size: 1.1rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Video Grid Section */
        .video-section {
            padding: 80px 0;
            background: var(--white);
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .video-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 220px;
            background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
            overflow: hidden;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.05);
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70px;
            height: 70px;
            background: rgba(210, 171, 104, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 1.5rem;
            transition: var(--transition);
            backdrop-filter: blur(10px);
        }

        .video-card:hover .play-button {
            background: var(--primary-color);
            transform: translate(-50%, -50%) scale(1.1);
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: var(--white);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .video-info {
            padding: 25px;
        }

        .video-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
            line-height: 1.3;
            font-family: var(--font-display);
        }

        .video-description {
            color: var(--text-secondary);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .video-date {
            font-weight: 500;
        }

        .video-views {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Loading Animation */
        .loading {
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
        }

        .loading.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-title {
                padding: 60px 0;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
            
            .video-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .video-section {
                padding: 60px 0;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 15px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .stat-item {
                padding: 25px 15px;
            }
            
            .video-info {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Page Title Section -->
    <section class="page-title">
        <div class="container">
            <div class="page-title-content">
                <h1>Video Guides</h1>
                <p class="subtitle">Essential video tutorials for Live Scan fingerprinting and notary services. Learn what to bring, how the process works, and tips for a smooth experience.</p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item loading">
                    <div class="stat-number">6</div>
                    <div class="stat-label">Video Guides</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number">2</div>
                    <div class="stat-label">Minutes Average</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">Free Access</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Available</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Guides Section -->
    <section class="video-section">
        <div class="container">
            <div class="section-header">
                <h2>Essential Video Guides</h2>
                <p>Watch our step-by-step tutorials to prepare for your Live Scan fingerprinting and notary appointments</p>
            </div>

            <div class="video-grid">
                <!-- Video 1: What is Live Scan Fingerprinting -->
                <div class="video-card loading" onclick="openVideo('XI3N_2TwQhM', 'What is Live Scan Fingerprinting?')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/02/live-scan-fingeprinting-thumb.jpg" alt="What is Live Scan Fingerprinting?" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">2:15</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">What is Live Scan Fingerprinting?</h3>
                        <p class="video-description">This video explains how Live Scan fingerprinting works and how it's used for background checks in California.</p>
                        <div class="video-meta">
                            <span class="video-date">Jan 7, 2019</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>2.1K views</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video 2: How to Visit Our Office -->
                <div class="video-card loading" onclick="openVideo('oidQw_iWmKo', 'How to Visit Our Office')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/02/office-trekking-thumb.jpg" alt="How to Visit Our Office" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">1:45</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">How to Visit Our Office</h3>
                        <p class="video-description">A quick walkthrough showing you how to find and enter our Los Angeles office for your scheduled Live Scan or notary appointment.</p>
                        <div class="video-meta">
                            <span class="video-date">Dec 6, 2023</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>1.8K views</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video 3: Mobile Live Scan Fingerprinting -->
                <div class="video-card loading" onclick="openVideo('vTLShIVulZ0', 'Mobile Live Scan Fingerprinting')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/02/mobile-live-scan-fingerprinting-video.jpg" alt="Mobile Live Scan Fingerprinting" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">2:30</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Mobile Live Scan Fingerprinting</h3>
                        <p class="video-description">Learn how our mobile Live Scan service brings certified fingerprinting technicians to your location—ideal for businesses, teams, or private clients.</p>
                        <div class="video-meta">
                            <span class="video-date">Aug 30, 2024</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>3.2K views</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video 4: Services for Self Determination Program -->
                <div class="video-card loading" onclick="openVideo('FZzbiFiTtYA', 'Services for the Self Determination Program')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/02/Special-services-video.jpg" alt="Services for the Self Determination Program" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">1:20</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">Services for the Self Determination Program</h3>
                        <p class="video-description">Our recent table at the Self Determination Program Resource Fair showcasing our specialized services.</p>
                        <div class="video-meta">
                            <span class="video-date">Oct 1, 2024</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>950 views</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video 5: Notary vs Apostille -->
                <div class="video-card loading" onclick="openVideo('sq5ImsWVnak', 'What\'s the Difference Between a Notary and an Apostille?')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-30-at-12.30.46%E2%80%AFPM.png" alt="Notary vs Apostille" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">1:20</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">What's the Difference Between a Notary and an Apostille?</h3>
                        <p class="video-description">This short video explains the key differences between a notarization and an apostille—including when you need each service.</p>
                        <div class="video-meta">
                            <span class="video-date">May 30, 2025</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>1.5K views</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video 6: How to Fill Out Live Scan Form -->
                <div class="video-card loading" onclick="openVideo('YNwhHdnsze8', 'How To Fill Out the Request for Live Scan Form Correctly')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-30-at-12.30.21%E2%80%AFPM.png" alt="How to Fill Out Live Scan Form" loading="lazy">
                        <div class="play-button">▶</div>
                        <div class="video-duration">1:32</div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">How To Fill Out the 'Request for Live Scan' Form Correctly</h3>
                        <p class="video-description">This quick tutorial walks you through how to properly complete the 'Request for Live Scan Service' form so your fingerprinting appointment goes smoothly.</p>
                        <div class="video-meta">
                            <span class="video-date">May 30, 2025</span>
                            <div class="video-views">
                                <span>👁</span>
                                <span>2.8K views</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <div class="section-header">
                <h2>Frequently Asked Questions</h2>
                <p>Common questions about our video guides and services</p>
            </div>

            <div class="faq-container">
                <!-- FAQ Item 1 -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>How long are your video tutorials for Live Scan?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Most of our Live Scan videos are under 2 minutes, giving you a fast and clear overview of what to expect. We respect your time and focus on the most important information.</p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Do you have a step-by-step video for first-time Live Scan users?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes. We offer a step-by-step video guide that explains exactly what to expect during your Live Scan appointment, including what to bring and how the fingerprinting process works. First-time users often find it reassuring to see the process in advance.</p>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Are there tutorials for preparing documents for notarization?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes, our notary video guides include simple instructions on how to prepare your documents, what forms of ID to bring, and common notary requirements. These are perfect for both individuals and businesses who need notarial services.</p>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Can I learn how to prepare for mobile fingerprinting through your videos?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Yes. We include short videos specifically for mobile Live Scan appointments so you'll know what to expect when we come to your home or business. No internet or power outlets are needed for our mobile fingerprinting setup.</p>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Are the video guides available in multiple languages?</span>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Currently, our video guides are in English. However, we're working to add subtitles and alternate language versions to make our services more accessible. Please reach out if you need support in another language.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Schedule Your Appointment?</h2>
                <p>Now that you've watched our helpful video guides, you're prepared for a smooth and efficient service experience. Contact us to schedule your Live Scan fingerprinting or notary appointment today.</p>
                <div class="cta-buttons">
                    <a href="https://anshinotary.com/contact/" class="cta-btn cta-btn-primary">
                        📞 Schedule Appointment
                    </a>
                    <a href="https://anshinotary.com/" class="cta-btn cta-btn-secondary">
                        🏠 Visit Homepage
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Modal -->
    <div id="videoModal" class="video-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"></h3>
                <button class="close-btn" onclick="closeVideoModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="video-container">
                    <iframe id="videoFrame" src="" frameborder="0" allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Video Modal Functions
        function openVideo(videoId, title) {
            const modal = document.getElementById('videoModal');
            const modalTitle = document.getElementById('modalTitle');
            const videoFrame = document.getElementById('videoFrame');

            modalTitle.textContent = title;
            videoFrame.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0`;
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const videoFrame = document.getElementById('videoFrame');

            modal.style.display = 'none';
            videoFrame.src = '';
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.getElementById('videoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVideoModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeVideoModal();
            }
        });

        // FAQ Toggle Functionality
        function toggleFAQ(element) {
            const faqItem = element.parentElement;
            const isActive = faqItem.classList.contains('active');

            // Close all FAQ items
            document.querySelectorAll('.faq-item').forEach(item => {
                item.classList.remove('active');
            });

            // Open clicked item if it wasn't already active
            if (!isActive) {
                faqItem.classList.add('active');
            }
        }

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.loading').forEach(element => {
                observer.observe(element);
            });
        });
    </script>

    <style>
        /* FAQ Section */
        .faq-section {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--white) 0%, var(--light-bg) 100%);
        }

        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .faq-item {
            background: var(--white);
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            box-shadow: var(--shadow-light);
            overflow: hidden;
            transition: var(--transition);
        }

        .faq-item:hover {
            box-shadow: var(--shadow-medium);
        }

        .faq-question {
            padding: 25px 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            transition: var(--transition);
        }

        .faq-question:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, #a67c52 100%);
        }

        .faq-icon {
            font-size: 1.2rem;
            transition: var(--transition);
            margin-left: 15px;
        }

        .faq-item.active .faq-icon {
            transform: rotate(45deg);
        }

        .faq-answer {
            padding: 0 30px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            background: var(--white);
        }

        .faq-item.active .faq-answer {
            padding: 25px 30px;
            max-height: 500px;
        }

        .faq-answer p {
            color: var(--text-secondary);
            line-height: 1.7;
            margin: 0;
            font-size: 1rem;
        }

        /* CTA Section */
        .cta-section {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
            color: var(--white);
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>');
            pointer-events: none;
        }

        .cta-content {
            position: relative;
            z-index: 2;
            max-width: 600px;
            margin: 0 auto;
        }

        .cta-section h2 {
            font-size: clamp(2rem, 4vw, 2.8rem);
            font-weight: 700;
            margin-bottom: 20px;
            font-family: var(--font-display);
        }

        .cta-section p {
            font-size: 1.1rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .cta-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .cta-btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .cta-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .cta-btn-secondary {
            background: transparent;
            color: var(--white);
            border: 2px solid var(--white);
        }

        .cta-btn-secondary:hover {
            background: var(--white);
            color: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* Video Modal Styles */
        .video-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 10000;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal-content {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            max-width: 900px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            position: relative;
        }

        .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--light-bg);
        }

        .modal-header h3 {
            font-family: var(--font-display);
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 2rem;
            color: var(--text-light);
            cursor: pointer;
            padding: 0;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: var(--transition);
        }

        .close-btn:hover {
            background: var(--border-color);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 0;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* Mobile Modal Styles */
        @media (max-width: 768px) {
            .video-modal {
                padding: 10px;
            }

            .modal-header {
                padding: 15px 20px;
            }

            .modal-header h3 {
                font-size: 1.2rem;
            }

            .faq-section {
                padding: 60px 0;
            }

            .faq-question {
                padding: 20px 25px;
                font-size: 1rem;
            }

            .faq-item.active .faq-answer {
                padding: 20px 25px;
            }

            .cta-section {
                padding: 60px 0;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }

            .cta-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
