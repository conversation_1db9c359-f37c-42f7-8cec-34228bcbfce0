<!DOCTYPE html>
<html lang="en-US" prefix="og: https://ogp.me/ns#">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>Testimonials - Anshin Mobile Notary &amp; LiveScan</title>

    <!-- Enhanced Meta Tags -->
    <meta name="description" content="Read verified testimonials from satisfied clients who used Anshin Mobile Notary for Live Scan fingerprinting, mobile notary, apostille processing, and FBI background checks in Los Angeles and Beverly Hills.">
    <meta name="keywords" content="testimonials, reviews, mobile notary, live scan, fingerprinting, Los Angeles, Beverly Hills">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" as="style">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Enhanced CSS Framework and Custom Styles -->
    <style>
        :root {
            --primary-color: #d2ab68;
            --primary-dark: #b8956b;
            --secondary-color: #2c3e50;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --white: #ffffff;
            --text-primary: #2c3e50;
            --text-secondary: #6c757d;
            --text-light: #95a5a6;
            --border-color: #e9ecef;
            --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
            --shadow-medium: 0 5px 20px rgba(0,0,0,0.15);
            --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-display: 'Playfair Display', Georgia, serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px;
        }

        body {
            font-family: var(--font-primary);
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--white);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .container-wide {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }



        /* Enhanced Page Title */
        .page-title {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, #a67c52 100%);
            color: white;
            text-align: center;
            padding: 80px 0;
            position: relative;
            overflow: hidden;
        }

        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .page-title-content {
            position: relative;
            z-index: 2;
        }

        .page-title h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 700;
            margin-bottom: 20px;
            font-family: var(--font-display);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.02em;
        }

        .page-title .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.5;
        }

        /* Stats Section */
        .stats-section {
            background: var(--white);
            padding: 60px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 30px 20px;
            border-radius: var(--border-radius-lg);
            background: linear-gradient(135deg, var(--light-bg) 0%, var(--white) 100%);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
            font-family: var(--font-display);
        }

        .stat-label {
            font-size: 1rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Filter Tabs */
        .filter-section {
            background: var(--white);
            padding: 40px 0 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .filter-tabs {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }

        .filter-tab {
            padding: 12px 24px;
            background: var(--light-bg);
            border: 2px solid transparent;
            border-radius: 25px;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .filter-tab:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
        }

        .filter-tab.active {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-dark);
        }

        /* Search Bar */
        .search-container {
            max-width: 400px;
            margin: 0 auto;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 25px;
            font-size: 16px;
            transition: var(--transition);
            background: var(--white);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(210, 171, 104, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            font-size: 18px;
        }

        /* Enhanced Featured Testimonials Grid */
        .featured-testimonials {
            padding: 80px 0;
            background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-header h2 {
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 15px;
            font-family: var(--font-display);
        }

        .section-header p {
            font-size: 1.1rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .testimonial-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            opacity: 0;
            transition: var(--transition);
            z-index: 1;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-heavy);
        }

        .testimonial-card:hover .card-overlay {
            opacity: 1;
        }

        .testimonial-card:hover .card-image img {
            transform: scale(1.05);
        }

        .card-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(210,171,104,0.9), rgba(52,152,219,0.8));
            opacity: 0;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .card-overlay span {
            color: var(--white);
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
            padding: 20px;
            background: rgba(0,0,0,0.2);
            border-radius: var(--border-radius);
            backdrop-filter: blur(5px);
        }

        .card-content {
            padding: 25px;
            position: relative;
            z-index: 2;
        }

        .card-content h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            line-height: 1.4;
            font-family: var(--font-display);
        }

        .card-content p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .card-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .card-tags span {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .card-tags span:hover {
            transform: scale(1.05);
        }

        .tag-mobile { background: #e3f2fd; color: #1976d2; }
        .tag-ink { background: #f3e5f5; color: #7b1fa2; }
        .tag-fbi { background: #e8f5e8; color: #2e7d32; }
        .tag-international { background: #fff3e0; color: #f57c00; }
        .tag-legal { background: #fce4ec; color: #c2185b; }
        .tag-livescan { background: #e3f2fd; color: #1976d2; }
        .tag-apostille { background: #f1f8e9; color: #558b2f; }
        .tag-notary { background: #fce4ec; color: #ad1457; }

        /* Enhanced Video Testimonials Section */
        .video-testimonials {
            padding: 80px 0;
            background: var(--white);
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
        }

        .video-card {
            background: var(--light-bg);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            cursor: pointer;
            position: relative;
        }

        .video-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
        }

        .video-card:hover .play-button {
            transform: translate(-50%, -50%) scale(1.1);
            background: var(--primary-color);
        }

        .video-thumbnail {
            position: relative;
            height: 220px;
            overflow: hidden;
            background: linear-gradient(45deg, var(--light-bg), #dee2e6);
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: var(--transition);
        }

        .video-card:hover .video-thumbnail img {
            transform: scale(1.05);
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 70px;
            height: 70px;
            background: rgba(210,171,104,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 28px;
            transition: var(--transition);
            box-shadow: var(--shadow-medium);
            backdrop-filter: blur(5px);
        }

        .play-button::before {
            content: '▶';
            margin-left: 3px;
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: var(--white);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .video-info {
            padding: 25px;
            background: var(--white);
        }

        .video-info h4 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
            line-height: 1.3;
            font-family: var(--font-display);
        }

        .video-info p {
            color: var(--text-secondary);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .video-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.8rem;
            color: var(--text-light);
        }

        .video-views {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .stars {
            color: #ffc107;
        }



        /* Loading Animation */
        .loading {
            opacity: 0;
            transform: translateY(20px);
            transition: var(--transition);
        }

        .loading.loaded {
            opacity: 1;
            transform: translateY(0);
        }

        /* Scroll to Top Button */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            color: var(--white);
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 20px;
            transition: var(--transition);
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }

        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            background: var(--primary-dark);
            transform: translateY(-3px);
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1024px) {
            .container {
                padding: 0 15px;
            }

            .testimonials-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
            }

            .video-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 25px;
            }
        }

        @media (max-width: 768px) {
            .page-title {
                padding: 60px 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .filter-tabs {
                gap: 8px;
            }

            .filter-tab {
                padding: 10px 16px;
                font-size: 13px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .video-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .scroll-to-top {
                bottom: 20px;
                right: 20px;
                width: 45px;
                height: 45px;
                font-size: 18px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 10px;
            }

            .page-title {
                padding: 40px 0;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .stat-item {
                padding: 20px 15px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .featured-testimonials,
            .video-testimonials {
                padding: 60px 0;
            }

            .section-header {
                margin-bottom: 40px;
            }

            .card-content {
                padding: 20px;
            }

            .video-info {
                padding: 20px;
            }
        }

        /* Dark Mode Support */
        @media (prefers-color-scheme: dark) {
            :root {
                --white: #1a1a1a;
                --light-bg: #2d2d2d;
                --text-primary: #e0e0e0;
                --text-secondary: #b0b0b0;
                --border-color: #404040;
            }
        }

        /* High Contrast Mode */
        @media (prefers-contrast: high) {
            .testimonial-card,
            .video-card {
                border: 2px solid var(--text-primary);
            }

            .filter-tab {
                border: 2px solid var(--text-primary);
            }
        }

        /* Reduced Motion */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            .scroll-to-top {
                transition: none;
            }
        }

        /* Print Styles */
        @media print {
            .header,
            .filter-section,
            .cta-section,
            .footer,
            .scroll-to-top {
                display: none;
            }

            .testimonial-card,
            .video-card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>

    <!-- Enhanced Page Title -->
    <section class="page-title">
        <div class="container">
            <div class="page-title-content">
                <h1>Client Testimonials</h1>
                <p class="subtitle">Discover authentic stories from our satisfied clients who experienced our professional notary and Live Scan services</p>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item loading">
                    <div class="stat-number" data-target="500">0</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number" data-target="4.9">0</div>
                    <div class="stat-label">Average Rating</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Available Service</div>
                </div>
                <div class="stat-item loading">
                    <div class="stat-number" data-target="100">0</div>
                    <div class="stat-label">Verified Reviews</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter and Search Section -->
    <section class="filter-section">
        <div class="container">
            <div class="filter-tabs">
                <div class="filter-tab active" data-filter="all">All Testimonials</div>
                <div class="filter-tab" data-filter="mobile-notary">Mobile Notary</div>
                <div class="filter-tab" data-filter="live-scan">Live Scan</div>
                <div class="filter-tab" data-filter="fbi-check">FBI Background Check</div>
                <div class="filter-tab" data-filter="apostille">Apostille Services</div>
            </div>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search testimonials..." id="searchInput">
                <span class="search-icon">🔍</span>
            </div>
        </div>
    </section>

    <!-- Featured Testimonials Grid Section -->
    <section class="featured-testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Featured Client Stories</h2>
                <p>Discover detailed testimonials from our satisfied clients who experienced our professional notary and Live Scan services.</p>
            </div>

            <div class="testimonials-grid">
                <!-- Testimonial Card 1 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/customer-testimonial-from-a-doctor-needing-mobile-ink-fingerprints-at-a-hospital/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-10.46.45%E2%80%AFAM-622x1024.png" alt="Doctor receiving mobile ink fingerprinting at hospital">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Mobile Ink Fingerprinting at Hospital</h3>
                        <p>A doctor shares their positive experience with our mobile ink fingerprinting service, completed on-site at Cedars-Sinai Hospital.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-ink">Ink Fingerprinting</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 2 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-fbi-background-check-for-french-visa-application/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-28-at-2.18.15%E2%80%AFPM-752x1024.png" alt="Mobile FBI background check for French visa">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>FBI Background Check for French Visa</h3>
                        <p>A client shares their experience receiving a quick and professional FBI background check for their French visa application.</p>
                        <div class="card-tags">
                            <span class="tag-fbi">FBI Background Check</span>
                            <span class="tag-international">International</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 3 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-live-scan-fingerprinting-for-attorney/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-03-at-12.14.37%E2%80%AFPM-759x1024.png" alt="Mobile live scan for attorney">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Attorney Licensing Fingerprinting</h3>
                        <p>A law firm representative describes our professional fingerprinting services for international licensing requirements.</p>
                        <div class="card-tags">
                            <span class="tag-legal">Legal Services</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 4 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-fingerprinting-for-group-of-camp-counselors/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-13-at-5.08.11%E2%80%AFPM-769x1024.png" alt="Mobile fingerprinting for camp counselors">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Group Fingerprinting for Camp Counselors</h3>
                        <p>Camp director describes our mobile fingerprinting services for a group of counselors in Los Angeles.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 5 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/restaurant-abc-license-mobile-live-scan/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/08/Screenshot-2025-08-04-at-2.11.33-PM-754x1024.png" alt="Restaurant ABC license mobile live scan">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Restaurant ABC License Compliance</h3>
                        <p>Restaurant staff receiving mobile live scan fingerprinting for California ABC liquor license application at their business location.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 6 -->
                <div class="testimonial-card loading" data-category="fbi-check apostille" onclick="window.open('https://anshinotary.com/testimonials/mobile-background-check-and-apostille-for-portugal-visa/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-16-at-4.43.59%E2%80%AFPM.png" alt="Mobile background check and apostille for Portugal visa" loading="lazy">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Background Check & Apostille for Portugal Visa</h3>
                        <p>A satisfied client who received mobile fingerprinting and apostille services for his Portuguese visa application.</p>
                        <div class="card-tags">
                            <span class="tag-fbi">Background Check</span>
                            <span class="tag-apostille">Apostille</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Video Testimonials Section -->
    <section class="video-testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Video Testimonials</h2>
                <p>Watch authentic video testimonials from our clients sharing their experiences with our services.</p>
            </div>

            <div class="video-grid" id="videoGrid">
                <!-- Video Card 1 -->
                <div class="video-card loading" data-category="live-scan" onclick="openVideoModal('Attorney Bar Licensing Fingerprinting', 'An attorney shares how our fingerprinting services helped with California Bar licensing requirements.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.23.42%E2%80%AFAM.png" alt="Attorney fingerprinting testimonial" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">2:15</div>
                    </div>
                    <div class="video-info">
                        <h4>Attorney Bar Licensing Fingerprinting</h4>
                        <p>An attorney shares how our fingerprinting services helped with California Bar licensing requirements.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 1.2k views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>5.0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Card 2 -->
                <div class="video-card loading" data-category="fbi-check" onclick="openVideoModal('Background Check for Volunteer Position', 'A client describes their experience getting a background check for a volunteer tutoring position.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.24.58%E2%80%AFAM.png" alt="Volunteer tutoring background check" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">1:45</div>
                    </div>
                    <div class="video-info">
                        <h4>Background Check for Volunteer Position</h4>
                        <p>A client describes their experience getting a background check for a volunteer tutoring position.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 890 views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>4.9</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Card 3 -->
                <div class="video-card loading" data-category="live-scan mobile-notary" onclick="openVideoModal('School Coach Compliance Services', 'A school golf coach shares how we provided background check, fingerprinting, and notary services.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.25.31%E2%80%AFAM.png" alt="School coach background check" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">3:20</div>
                    </div>
                    <div class="video-info">
                        <h4>School Coach Compliance Services</h4>
                        <p>A school golf coach shares how we provided background check, fingerprinting, and notary services.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 1.5k views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>5.0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Card 4 -->
                <div class="video-card loading" data-category="fbi-check" onclick="openVideoModal('Rush Ink Card Fingerprinting', 'A client shares how we delivered fast ink card fingerprinting for financial services background check.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.25.55%E2%80%AFAM.png" alt="Financial services fingerprinting" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">1:30</div>
                    </div>
                    <div class="video-info">
                        <h4>Rush Ink Card Fingerprinting</h4>
                        <p>A client shares how we delivered fast ink card fingerprinting for financial services background check.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 750 views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>4.8</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Card 5 -->
                <div class="video-card loading" data-category="fbi-check" onclick="openVideoModal('Real Estate License Background Check', 'A satisfied client shares their experience getting a background check for California real estate license.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.26.44%E2%80%AFAM.png" alt="Real estate license background check" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">2:05</div>
                    </div>
                    <div class="video-info">
                        <h4>Real Estate License Background Check</h4>
                        <p>A satisfied client shares their experience getting a background check for California real estate license.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 1.1k views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>4.9</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Card 6 -->
                <div class="video-card loading" data-category="mobile-notary" onclick="openVideoModal('Mobile Notary for Business Licensing', 'A client describes how our mobile notary services helped with business license paperwork in Los Angeles.')">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.27.08%E2%80%AFAM.png" alt="Mobile notary business licensing" loading="lazy">
                        <div class="play-button"></div>
                        <div class="video-duration">2:40</div>
                    </div>
                    <div class="video-info">
                        <h4>Mobile Notary for Business Licensing</h4>
                        <p>A client describes how our mobile notary services helped with business license paperwork in Los Angeles.</p>
                        <div class="video-meta">
                            <div class="video-views">👁 980 views</div>
                            <div class="video-rating">
                                <span class="stars">⭐⭐⭐⭐⭐</span>
                                <span>5.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- JavaScript for Interactive Features -->
    <script>
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add click handlers for video cards
        document.querySelectorAll('.video-card').forEach(card => {
            card.addEventListener('click', function() {
                // In a real implementation, you would open a video modal or lightbox
                // For now, we'll just show an alert
                const title = this.querySelector('h4').textContent;
                alert('Video: ' + title + '\n\nIn a real implementation, this would open the video in a modal or lightbox.');
            });
        });

        // Add loading animation for images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', function() {
                this.style.opacity = '1';
            });
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe cards for animation
        document.querySelectorAll('.testimonial-card, .video-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>

    <!-- Scroll to Top Button -->
    <button class="scroll-to-top" id="scrollToTop" onclick="scrollToTop()">↑</button>

    <!-- Video Modal -->
    <div id="videoModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); z-index: 10000; align-items: center; justify-content: center;">
        <div style="background: white; padding: 30px; border-radius: 15px; max-width: 600px; width: 90%; text-align: center;">
            <h3 id="modalTitle" style="margin-bottom: 15px; color: var(--text-primary);"></h3>
            <p id="modalDescription" style="margin-bottom: 20px; color: var(--text-secondary);"></p>
            <p style="color: var(--text-light); font-style: italic;">Video player would be implemented here in a real application.</p>
            <button onclick="closeVideoModal()" style="margin-top: 20px; padding: 10px 20px; background: var(--primary-color); color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
        </div>
    </div>

    <!-- Enhanced JavaScript -->
    <script>
        // Animated Counter for Stats
        function animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (target === 4.9) {
                    element.textContent = current.toFixed(1);
                } else if (target === 100) {
                    element.textContent = Math.floor(current) + '%';
                } else {
                    element.textContent = Math.floor(current) + '+';
                }
            }, 16);
        }

        // Filter Functionality
        const filterTabs = document.querySelectorAll('.filter-tab');
        const testimonialCards = document.querySelectorAll('.testimonial-card');
        const videoCards = document.querySelectorAll('.video-card');

        filterTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                filterTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');

                [...testimonialCards, ...videoCards].forEach(card => {
                    if (filter === 'all' || (card.getAttribute('data-category') && card.getAttribute('data-category').includes(filter))) {
                        card.style.display = 'block';
                        setTimeout(() => {
                            card.style.opacity = '1';
                            card.style.transform = 'translateY(0)';
                        }, 100);
                    } else {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';
                        setTimeout(() => {
                            card.style.display = 'none';
                        }, 300);
                    }
                });
            });
        });

        // Search Functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                [...testimonialCards, ...videoCards].forEach(card => {
                    const title = card.querySelector('h3, h4').textContent.toLowerCase();
                    const description = card.querySelector('p').textContent.toLowerCase();

                    if (title.includes(searchTerm) || description.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        }

        // Video Modal Functions
        function openVideoModal(title, description) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalDescription').textContent = description;
            document.getElementById('videoModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeVideoModal() {
            document.getElementById('videoModal').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Scroll to Top
        const scrollToTopBtn = document.getElementById('scrollToTop');

        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Enhanced Intersection Observer
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');

                    // Animate counters when stats section comes into view
                    if (entry.target.classList.contains('stat-item')) {
                        const numberElement = entry.target.querySelector('.stat-number');
                        const target = numberElement.getAttribute('data-target');
                        if (target) {
                            animateCounter(numberElement, parseFloat(target));
                        }
                    }
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.loading').forEach(element => {
            observer.observe(element);
        });

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeVideoModal();
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
