<!DOCTYPE html>
<html lang="en-US" prefix="og: https://ogp.me/ns#">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>Testimonials - Anshin Mobile Notary &amp; LiveScan</title>
    
    <!-- Modern CSS Framework and Custom Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header Styles */
        .header {
            background: #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-top {
            background: #363537;
            color: #fff;
            padding: 10px 0;
            font-size: 14px;
        }
        
        .header-top .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .location-info {
            display: flex;
            gap: 30px;
        }
        
        .nav-bar {
            background: #fff;
            padding: 15px 0;
        }
        
        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo img {
            height: 60px;
            width: auto;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }
        
        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-menu a:hover {
            color: #d2ab68;
        }
        
        /* Key Facts Section */
        .key-facts {
            background: #f8f9fa;
            padding: 30px 0;
            border-left: 4px solid #d2ab68;
        }
        
        .key-facts h2 {
            color: #333;
            margin-bottom: 20px;
            font-family: 'Signika', sans-serif;
        }
        
        .key-facts ul {
            list-style: none;
            padding-left: 0;
        }
        
        .key-facts li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        
        .key-facts li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #d2ab68;
            font-weight: bold;
        }
        
        /* Page Title */
        .page-title {
            background: linear-gradient(135deg, #d2ab68 0%, #e6c485 100%);
            color: white;
            text-align: center;
            padding: 60px 0;
        }
        
        .page-title h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 15px;
            font-family: 'Signika', sans-serif;
        }
        
        /* Featured Testimonials Grid */
        .featured-testimonials {
            padding: 80px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            font-family: 'Signika', sans-serif;
        }
        
        .section-header p {
            font-size: 1.1rem;
            color: #6c757d;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .testimonial-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .testimonial-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .testimonial-card:hover .card-overlay {
            opacity: 1;
        }
        
        .testimonial-card:hover .card-image img {
            transform: scale(1.05);
        }
        
        .card-image {
            position: relative;
            overflow: hidden;
            height: 250px;
        }
        
        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .card-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(210,171,104,0.9), rgba(210,171,104,0.7));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .card-overlay span {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            text-align: center;
            padding: 20px;
        }
        
        .card-content {
            padding: 25px;
        }
        
        .card-content h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .card-content p {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }
        
        .card-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .card-tags span {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .tag-mobile { background: #e3f2fd; color: #1976d2; }
        .tag-ink { background: #f3e5f5; color: #7b1fa2; }
        .tag-fbi { background: #e8f5e8; color: #2e7d32; }
        .tag-international { background: #fff3e0; color: #f57c00; }
        .tag-legal { background: #fce4ec; color: #c2185b; }
        .tag-livescan { background: #e3f2fd; color: #1976d2; }
        
        /* Video Testimonials Section */
        .video-testimonials {
            padding: 80px 0;
            background: #fff;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }
        
        .video-card {
            background: #f8f9fa;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .video-thumbnail {
            position: relative;
            height: 200px;
            overflow: hidden;
        }
        
        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(210,171,104,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: all 0.3s ease;
        }
        
        .play-button:hover {
            background: rgba(210,171,104,1);
            transform: translate(-50%, -50%) scale(1.1);
        }
        
        .video-info {
            padding: 20px;
        }
        
        .video-info h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .video-info p {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        /* Footer */
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 0 20px;
        }
        
        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .social-icons {
            display: flex;
            gap: 15px;
        }
        
        .social-icons a {
            width: 40px;
            height: 40px;
            background: #d2ab68;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .social-icons a:hover {
            background: #b8956b;
            transform: translateY(-2px);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .header-top .container {
                flex-direction: column;
                gap: 10px;
            }
            
            .location-info {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }
            
            .nav-container {
                flex-direction: column;
                gap: 20px;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
                gap: 15px;
            }
            
            .page-title h1 {
                font-size: 2rem;
            }
            
            .section-header h2 {
                font-size: 2rem;
            }
            
            .testimonials-grid {
                grid-template-columns: 1fr;
            }
            
            .video-grid {
                grid-template-columns: 1fr;
            }
            
            .footer-content {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-top">
            <div class="container">
                <div class="location-info">
                    <div>📍 Los Angeles 📞 (*************</div>
                    <div>📍 Beverly Hills 📞 (*************</div>
                </div>
            </div>
        </div>
        <nav class="nav-bar">
            <div class="container">
                <div class="nav-container">
                    <div class="logo">
                        <a href="https://anshinotary.com">
                            <img src="https://anshinotary.com/wp-content/uploads/2020/10/Anshin-notary-logo.png" alt="Anshin Mobile Notary Logo">
                        </a>
                    </div>
                    <ul class="nav-menu">
                        <li><a href="https://anshinotary.com/">Home</a></li>
                        <li><a href="#">Services</a></li>
                        <li><a href="https://anshinotary.com/contact/">Contact</a></li>
                        <li><a href="https://anshinotary.com/video-guides-for-our-live-scan-and-notary-services/">Videos</a></li>
                        <li><a href="https://anshinotary.com/forms/">Live Scan Forms</a></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Key Facts Section (Preserved) -->
    <section class="key-facts">
        <div class="container">
            <h2>Key Facts</h2>
            <ul>
                <li>All <strong>testimonials</strong> are from verified customers who used our notary and related services.</li>
                <li>Clients leave reviews for services like <strong>mobile notary</strong>, <strong>Live Scan fingerprinting</strong>, and <strong>FBI background checks</strong>.</li>
                <li>Reviews highlight our <strong>flexibility</strong>, same-day availability, and <strong>friendly professionalism</strong>.</li>
                <li>Customer feedback is collected through direct communication, emails, and reviews on platforms like <strong>Google</strong> and <strong>Yelp</strong>.</li>
                <li>We encourage clients to share their own <strong>testimonials</strong>, whether written or video, to help others.</li>
            </ul>
        </div>
    </section>

    <!-- Page Title -->
    <section class="page-title">
        <div class="container">
            <h1>Testimonials</h1>
        </div>
    </section>

    <!-- Featured Testimonials Grid Section -->
    <section class="featured-testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Featured Client Stories</h2>
                <p>Discover detailed testimonials from our satisfied clients who experienced our professional notary and Live Scan services.</p>
            </div>

            <div class="testimonials-grid">
                <!-- Testimonial Card 1 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/customer-testimonial-from-a-doctor-needing-mobile-ink-fingerprints-at-a-hospital/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-10.46.45%E2%80%AFAM-622x1024.png" alt="Doctor receiving mobile ink fingerprinting at hospital">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Mobile Ink Fingerprinting at Hospital</h3>
                        <p>A doctor shares their positive experience with our mobile ink fingerprinting service, completed on-site at Cedars-Sinai Hospital.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-ink">Ink Fingerprinting</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 2 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-fbi-background-check-for-french-visa-application/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-28-at-2.18.15%E2%80%AFPM-752x1024.png" alt="Mobile FBI background check for French visa">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>FBI Background Check for French Visa</h3>
                        <p>A client shares their experience receiving a quick and professional FBI background check for their French visa application.</p>
                        <div class="card-tags">
                            <span class="tag-fbi">FBI Background Check</span>
                            <span class="tag-international">International</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 3 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-live-scan-fingerprinting-for-attorney/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-03-at-12.14.37%E2%80%AFPM-759x1024.png" alt="Mobile live scan for attorney">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Attorney Licensing Fingerprinting</h3>
                        <p>A law firm representative describes our professional fingerprinting services for international licensing requirements.</p>
                        <div class="card-tags">
                            <span class="tag-legal">Legal Services</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 4 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-fingerprinting-for-group-of-camp-counselors/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-13-at-5.08.11%E2%80%AFPM-769x1024.png" alt="Mobile fingerprinting for camp counselors">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Group Fingerprinting for Camp Counselors</h3>
                        <p>Camp director describes our mobile fingerprinting services for a group of counselors in Los Angeles.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 5 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/restaurant-abc-license-mobile-live-scan/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/08/Screenshot-2025-08-04-at-2.11.33-PM-754x1024.png" alt="Restaurant ABC license mobile live scan">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Restaurant ABC License Compliance</h3>
                        <p>Restaurant staff receiving mobile live scan fingerprinting for California ABC liquor license application at their business location.</p>
                        <div class="card-tags">
                            <span class="tag-mobile">Mobile Service</span>
                            <span class="tag-livescan">Live Scan</span>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 6 -->
                <div class="testimonial-card" onclick="window.open('https://anshinotary.com/testimonials/mobile-background-check-and-apostille-for-portugal-visa/', '_blank')">
                    <div class="card-image">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/06/Screenshot-2025-06-16-at-4.43.59%E2%80%AFPM.png" alt="Mobile background check and apostille for Portugal visa">
                        <div class="card-overlay">
                            <span>Read Full Story →</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <h3>Background Check & Apostille for Portugal Visa</h3>
                        <p>A satisfied client who received mobile fingerprinting and apostille services for his Portuguese visa application.</p>
                        <div class="card-tags">
                            <span class="tag-fbi">Background Check</span>
                            <span class="tag-international">International</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Video Testimonials Section -->
    <section class="video-testimonials">
        <div class="container">
            <div class="section-header">
                <h2>Video Testimonials</h2>
                <p>Watch authentic video testimonials from our clients sharing their experiences with our services.</p>
            </div>

            <div class="video-grid">
                <!-- Video Card 1 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.23.42%E2%80%AFAM.png" alt="Attorney fingerprinting testimonial">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>Attorney Bar Licensing Fingerprinting</h4>
                        <p>An attorney shares how our fingerprinting services helped with California Bar licensing requirements.</p>
                    </div>
                </div>

                <!-- Video Card 2 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.24.58%E2%80%AFAM.png" alt="Volunteer tutoring background check">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>Background Check for Volunteer Position</h4>
                        <p>A client describes their experience getting a background check for a volunteer tutoring position.</p>
                    </div>
                </div>

                <!-- Video Card 3 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.25.31%E2%80%AFAM.png" alt="School coach background check">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>School Coach Compliance Services</h4>
                        <p>A school golf coach shares how we provided background check, fingerprinting, and notary services.</p>
                    </div>
                </div>

                <!-- Video Card 4 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.25.55%E2%80%AFAM.png" alt="Financial services fingerprinting">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>Rush Ink Card Fingerprinting</h4>
                        <p>A client shares how we delivered fast ink card fingerprinting for financial services background check.</p>
                    </div>
                </div>

                <!-- Video Card 5 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.26.44%E2%80%AFAM.png" alt="Real estate license background check">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>Real Estate License Background Check</h4>
                        <p>A satisfied client shares their experience getting a background check for California real estate license.</p>
                    </div>
                </div>

                <!-- Video Card 6 -->
                <div class="video-card">
                    <div class="video-thumbnail">
                        <img src="https://anshinotary.com/wp-content/uploads/2025/05/Screenshot-2025-05-22-at-11.27.08%E2%80%AFAM.png" alt="Mobile notary business licensing">
                        <div class="play-button">▶</div>
                    </div>
                    <div class="video-info">
                        <h4>Mobile Notary for Business Licensing</h4>
                        <p>A client describes how our mobile notary services helped with business license paperwork in Los Angeles.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div>
                    <p>© All rights reserved 2025 Anshin Mobile Notary and LiveScan</p>
                </div>
                <div class="social-icons">
                    <a href="https://www.facebook.com/Anshinotaryla" target="_blank" title="Facebook">f</a>
                    <a href="https://www.linkedin.com/company/anshin-mobile-notary-livescan-los-angeles/" target="_blank" title="LinkedIn">in</a>
                    <a href="https://medium.com/@aaronanshin" target="_blank" title="Medium">M</a>
                    <a href="https://www.youtube.com/@anshinotary" target="_blank" title="YouTube">▶</a>
                    <a href="https://www.yelp.com/biz/anshin-mobile-notary-and-livescan-los-angeles-7" target="_blank" title="Yelp">Y</a>
                    <a href="https://g.page/r/CZ04fHy9V784EB0" target="_blank" title="Google">G</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript for Interactive Features -->
    <script>
        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add click handlers for video cards
        document.querySelectorAll('.video-card').forEach(card => {
            card.addEventListener('click', function() {
                // In a real implementation, you would open a video modal or lightbox
                // For now, we'll just show an alert
                const title = this.querySelector('h4').textContent;
                alert('Video: ' + title + '\n\nIn a real implementation, this would open the video in a modal or lightbox.');
            });
        });

        // Add loading animation for images
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('load', function() {
                this.style.opacity = '1';
            });
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe cards for animation
        document.querySelectorAll('.testimonial-card, .video-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>
